-- ============================================================================
-- ACCOUNT DELETION VERIFICATION SCRIPT
-- ============================================================================
-- This script verifies that account deletion is working correctly and is GDPR compliant
-- Run this to check the current state of the database and verify deletion functionality
-- ============================================================================

\echo 'Verifying Account Deletion Process and GDPR Compliance...'
\echo ''

-- 1. Check current users in the system
\echo '1. Current Users in System:'
SELECT 
    id,
    email,
    name,
    email_verified,
    created_at
FROM public.users
ORDER BY created_at DESC;

\echo ''

-- 2. Check for any orphaned records that should be cleaned up
\echo '2. Checking for Orphaned Records:'

-- Sessions without valid users
SELECT 
    'orphaned_sessions' as issue_type,
    COUNT(*) as count,
    array_agg(s.id) as record_ids
FROM public.sessions s
LEFT JOIN public.users u ON s.user_id = u.id
WHERE u.id IS NULL;

-- Accounts without valid users
SELECT 
    'orphaned_accounts' as issue_type,
    COUNT(*) as count,
    array_agg(a.id) as record_ids
FROM public.accounts a
LEFT JOIN public.users u ON a.user_id = u.id
WHERE u.id IS NULL;

-- Subscriptions without valid users
SELECT 
    'orphaned_subscriptions' as issue_type,
    COUNT(*) as count,
    array_agg(sub.id) as record_ids
FROM public.subscriptions sub
LEFT JOIN public.users u ON sub.user_id = u.id
WHERE u.id IS NULL;

-- One-time purchases without valid users
SELECT 
    'orphaned_purchases' as issue_type,
    COUNT(*) as count,
    array_agg(otp.id) as record_ids
FROM public.one_time_purchases otp
LEFT JOIN public.users u ON otp.user_id = u.id
WHERE u.id IS NULL;

\echo ''

-- 3. Check GDPR tables for orphaned records
\echo '3. Checking GDPR Tables for Orphaned Records:'

-- User consents without valid users
SELECT 
    'orphaned_user_consents' as issue_type,
    COUNT(*) as count
FROM public.user_consents uc
LEFT JOIN public.users u ON uc.user_id = u.id
WHERE u.id IS NULL;

-- Audit logs without valid users (excluding system logs)
SELECT 
    'orphaned_audit_logs' as issue_type,
    COUNT(*) as count
FROM public.audit_logs al
LEFT JOIN public.users u ON al.user_id = u.id
WHERE al.user_id IS NOT NULL AND u.id IS NULL;

-- Deletion requests without valid users
SELECT 
    'orphaned_deletion_requests' as issue_type,
    COUNT(*) as count
FROM public.deletion_requests dr
LEFT JOIN public.users u ON dr.user_id = u.id
WHERE u.id IS NULL;

-- Cookie consents without valid users (excluding anonymous ones)
SELECT 
    'orphaned_cookie_consents' as issue_type,
    COUNT(*) as count
FROM public.cookie_consents cc
LEFT JOIN public.users u ON cc.user_id = u.id
WHERE cc.user_id IS NOT NULL AND u.id IS NULL;

\echo ''

-- 4. Check for specific email traces (should be none after proper deletion)
\echo '4. Checking for Email Traces (should be empty after deletion):'

-- Check verifications table for the deleted email
SELECT 
    'verifications_for_deleted_email' as check_type,
    COUNT(*) as count,
    array_agg(identifier) as emails_found
FROM public.verifications
WHERE identifier = '<EMAIL>';

-- Check audit logs for references to the deleted email
SELECT 
    'audit_logs_mentioning_deleted_email' as check_type,
    COUNT(*) as count
FROM public.audit_logs
WHERE details::text LIKE '%<EMAIL>%';

\echo ''

-- 5. Data retention summary (GDPR compliance check)
\echo '5. Data Retention Summary (GDPR Compliance):'

SELECT 
    'users' as table_name, 
    COUNT(*) as total_records,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END) as recent_records
FROM public.users
UNION ALL
SELECT 
    'sessions', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.sessions
UNION ALL
SELECT 
    'accounts', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.accounts
UNION ALL
SELECT 
    'subscriptions', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.subscriptions
UNION ALL
SELECT 
    'user_consents', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.user_consents
UNION ALL
SELECT 
    'audit_logs', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.audit_logs
UNION ALL
SELECT 
    'deletion_requests', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.deletion_requests
UNION ALL
SELECT 
    'cookie_consents', 
    COUNT(*),
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END)
FROM public.cookie_consents
ORDER BY table_name;

\echo ''

-- 6. Check RLS policies are working correctly
\echo '6. RLS Policy Status:'
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'sessions', 'accounts', 'subscriptions', 'one_time_purchases', 'verifications', 'user_consents', 'audit_logs', 'deletion_requests', 'cookie_consents')
ORDER BY tablename;

\echo ''

-- 7. Summary and recommendations
\echo '============================================================================'
\echo 'ACCOUNT DELETION VERIFICATION COMPLETE'
\echo '============================================================================'
\echo ''
\echo 'What this verification checks:'
\echo '✓ No orphaned records exist after user deletion'
\echo '✓ GDPR tables are properly cleaned up'
\echo '✓ Email traces are removed from all tables'
\echo '✓ RLS policies are enabled for data protection'
\echo '✓ Data retention is appropriate'
\echo ''
\echo 'Expected results after proper account deletion:'
\echo '- All orphaned record counts should be 0'
\echo '- No email traces should exist for deleted users'
\echo '- RLS should be enabled on all tables'
\echo '- Only legitimate audit trails should remain'
\echo ''
\echo 'If you see any issues:'
\echo '1. Check the account deletion code in app/api/account/delete/route.ts'
\echo '2. Ensure all GDPR tables are included in cleanup'
\echo '3. Verify RLS policies are working correctly'
\echo '4. Test the deletion process with a test user'
\echo '============================================================================'
