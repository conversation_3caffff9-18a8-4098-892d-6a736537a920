/**
 * Test API for Creem Integration Verification
 * 
 * This endpoint helps test and verify that Creem subscription management is working correctly.
 * Only available in development mode for security.
 */

import { NextRequest, NextResponse } from 'next/server';
import { Creem } from 'creem';
import { supabaseAdmin } from '@/lib/supabase';
import { subscriptionOperations } from '@/lib/database';

export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const subscriptionId = searchParams.get('subscriptionId');
    const userId = searchParams.get('userId');

    const apiKey = process.env.CREEM_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'CREEM_API_KEY not configured' },
        { status: 500 }
      );
    }

    const creem = new Creem({
      serverIdx: 1, // Test environment
    });

    switch (action) {
      case 'test-connection':
        try {
          // Test basic Creem API connection
          // Note: This might fail if there's no specific test endpoint
          return NextResponse.json({
            success: true,
            message: 'Creem SDK initialized successfully',
            config: {
              serverIdx: 1,
              hasApiKey: !!apiKey
            }
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            error: `Creem connection failed: ${error}`
          });
        }

      case 'list-user-subscriptions':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId parameter is required' },
            { status: 400 }
          );
        }

        try {
          const subscriptions = await subscriptionOperations.findByUserId(userId);
          return NextResponse.json({
            success: true,
            userId,
            subscriptions: subscriptions.map(sub => ({
              id: sub.id,
              status: sub.status,
              product: sub.product,
              created_at: sub.created_at,
              updated_at: sub.updated_at
            }))
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            error: `Failed to fetch subscriptions: ${error}`
          });
        }

      case 'test-cancel-subscription':
        if (!subscriptionId) {
          return NextResponse.json(
            { error: 'subscriptionId parameter is required' },
            { status: 400 }
          );
        }

        try {
          // Test canceling a subscription in Creem
          await creem.cancelSubscription({
            xApiKey: apiKey,
            id: subscriptionId
          });

          return NextResponse.json({
            success: true,
            message: `Subscription ${subscriptionId} canceled successfully in Creem`,
            subscriptionId
          });
        } catch (error: any) {
          return NextResponse.json({
            success: false,
            error: `Failed to cancel subscription in Creem: ${error.message || error}`,
            subscriptionId
          });
        }

      case 'verify-subscription-status':
        if (!subscriptionId) {
          return NextResponse.json(
            { error: 'subscriptionId parameter is required' },
            { status: 400 }
          );
        }

        try {
          // Check subscription status in our database
          const { data: subscription, error } = await supabaseAdmin
            .from('subscriptions')
            .select('*')
            .eq('id', subscriptionId)
            .single();

          if (error) {
            throw error;
          }

          return NextResponse.json({
            success: true,
            subscription: subscription || null,
            message: subscription 
              ? `Subscription found with status: ${subscription.status}`
              : 'Subscription not found in database'
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            error: `Failed to verify subscription status: ${error}`
          });
        }

      case 'simulate-account-deletion-check':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId parameter is required' },
            { status: 400 }
          );
        }

        try {
          // Simulate the subscription cancellation check from account deletion
          const userSubscriptions = await subscriptionOperations.findByUserId(userId);
          const activeSubscriptions = userSubscriptions.filter(s => s.status === 'active');

          const results = {
            userId,
            totalSubscriptions: userSubscriptions.length,
            activeSubscriptions: activeSubscriptions.length,
            subscriptions: userSubscriptions.map(sub => ({
              id: sub.id,
              status: sub.status,
              product: sub.product
            })),
            canDeleteAccount: activeSubscriptions.length === 0,
            message: activeSubscriptions.length > 0 
              ? `Cannot delete account: ${activeSubscriptions.length} active subscriptions must be canceled first`
              : 'Account can be safely deleted - no active subscriptions'
          };

          return NextResponse.json({
            success: true,
            ...results
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            error: `Failed to check account deletion eligibility: ${error}`
          });
        }

      default:
        return NextResponse.json({
          success: true,
          message: 'Creem integration test API',
          availableActions: [
            'test-connection - Test Creem API connection',
            'list-user-subscriptions?userId=xxx - List user subscriptions',
            'test-cancel-subscription?subscriptionId=xxx - Test subscription cancellation',
            'verify-subscription-status?subscriptionId=xxx - Check subscription status',
            'simulate-account-deletion-check?userId=xxx - Check if account can be safely deleted'
          ]
        });
    }

  } catch (error: any) {
    console.error('Creem integration test API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const body = await request.json();
    const { action, subscriptionId, userId } = body;

    const apiKey = process.env.CREEM_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'CREEM_API_KEY not configured' },
        { status: 500 }
      );
    }

    const creem = new Creem({
      serverIdx: 1, // Test environment
    });

    switch (action) {
      case 'force-cancel-subscription':
        if (!subscriptionId) {
          return NextResponse.json(
            { error: 'subscriptionId is required' },
            { status: 400 }
          );
        }

        try {
          // Cancel in Creem
          await creem.cancelSubscription({
            xApiKey: apiKey,
            id: subscriptionId
          });

          // Update local database
          await subscriptionOperations.update(subscriptionId, {
            status: "canceled",
            updated_at: new Date().toISOString(),
          });

          return NextResponse.json({
            success: true,
            message: `Subscription ${subscriptionId} canceled in both Creem and local database`,
            subscriptionId
          });
        } catch (error: any) {
          return NextResponse.json({
            success: false,
            error: `Failed to cancel subscription: ${error.message || error}`,
            subscriptionId
          });
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Creem integration test API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
