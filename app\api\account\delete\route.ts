import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import {
  sessionOperations,
  subscriptionOperations
} from "@/lib/database";
import { supabaseAdmin } from "@/lib/supabase";
import { SubscriptionStatus } from "@/types/database";

/**
 * DELETE /api/account/delete
 *
 * Permanently delete user account and all associated data
 * This is a GDPR-compliant hard delete that removes all user data
 *
 * Security Requirements:
 * - User must be authenticated
 * - Password verification required
 * - All related data must be cleaned up
 * - Third-party services must be notified
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get authenticated session
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { password, confirmation } = body;

    // Validate required fields
    if (!password) {
      return NextResponse.json(
        { error: "Password is required for account deletion" },
        { status: 400 }
      );
    }

    if (confirmation !== "DELETE FOREVER") {
      return NextResponse.json(
        { error: 'Please type "DELETE FOREVER" to confirm' },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log(`Starting account deletion for user: ${userId}`);

    // Create audit log entry before deletion starts (GDPR compliance)
    try {
      // Use direct SQL query since GDPR tables aren't in TypeScript types
      const { error: auditError } = await supabaseAdmin
        .from('audit_logs' as any)
        .insert({
          user_id: userId,
          action: 'account_deletion_started',
          resource: 'user_account',
          details: {
            email: userEmail,
            ip_address: request.headers.get('x-forwarded-for') || 'unknown',
            user_agent: request.headers.get('user-agent') || 'unknown',
            timestamp: new Date().toISOString()
          },
          ip_address: request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        });

      if (auditError) {
        console.error('Failed to create audit log entry:', auditError);
      }
    } catch (error) {
      console.error('Failed to create audit log entry:', error);
      // Continue with deletion even if audit log fails
    }

    // Step 1: Cancel all active subscriptions in Creem FIRST
    // This is CRITICAL to prevent continued billing after account deletion
    try {
      const userSubscriptions = await subscriptionOperations.findByUserId(userId);
      const { Creem } = await import('creem');

      const creem = new Creem({
        serverIdx: 1, // Test environment
      });

      const apiKey = process.env.CREEM_API_KEY;
      if (!apiKey) {
        console.error('CREEM_API_KEY not found - cannot cancel subscriptions!');
        return NextResponse.json(
          { error: "Payment system configuration error. Cannot safely delete account." },
          { status: 500 }
        );
      }

      for (const subscription of userSubscriptions) {
        if (subscription.status === 'active') {
          try {
            console.log(`Canceling subscription in Creem: ${subscription.id}`);

            // Cancel subscription in Creem to prevent future billing
            await creem.cancelSubscription({
              xApiKey: apiKey,
              id: subscription.id
            });

            console.log(`Successfully canceled subscription in Creem: ${subscription.id}`);

            // Update local database to reflect cancellation immediately
            // This prevents race conditions with incoming webhooks
            await subscriptionOperations.update(subscription.id, {
              status: "canceled" as SubscriptionStatus,
              updated_at: new Date().toISOString(),
            });

            console.log(`Updated local database for subscription: ${subscription.id}`);

          } catch (error) {
            console.error(`CRITICAL: Failed to cancel subscription ${subscription.id} in Creem:`, error);
            // This is critical - if we can't cancel in Creem, we shouldn't delete the account
            return NextResponse.json(
              { error: `Failed to cancel subscription ${subscription.id}. Account deletion aborted to prevent continued billing.` },
              { status: 500 }
            );
          }
        }
      }

      console.log(`Successfully canceled ${userSubscriptions.filter(s => s.status === 'active').length} active subscriptions`);

      // Allow a brief moment for Creem webhooks to be processed
      // This prevents race conditions where webhooks arrive after user deletion
      if (userSubscriptions.some(s => s.status === 'active')) {
        console.log('Waiting 2 seconds for Creem webhooks to be processed...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Verify all subscriptions are canceled before proceeding
      const remainingActiveSubscriptions = await subscriptionOperations.findByUserId(userId);
      const stillActive = remainingActiveSubscriptions.filter(s => s.status === 'active');

      if (stillActive.length > 0) {
        console.error(`CRITICAL: ${stillActive.length} subscriptions still active after cancellation attempt`);
        return NextResponse.json(
          { error: "Some subscriptions could not be canceled. Account deletion aborted to prevent continued billing." },
          { status: 500 }
        );
      }

    } catch (error) {
      console.error('CRITICAL: Error canceling subscriptions in Creem:', error);
      return NextResponse.json(
        { error: "Failed to cancel subscriptions. Account deletion aborted to prevent continued billing." },
        { status: 500 }
      );
    }

    // Step 2: Delete the user account using Better Auth FIRST
    // This must be done before deleting sessions to maintain authentication
    try {
      // Use Better Auth's deleteUser method which will handle the user table deletion
      await auth.api.deleteUser({
        headers: headers(),
        body: { password }
      });

      console.log(`Successfully deleted user account via Better Auth: ${userId}`);

    } catch (error: any) {
      console.error('Error deleting user account via Better Auth:', error);
      // Continue with manual deletion if Better Auth fails
      console.log('Attempting manual user deletion from users table...');
    }

    // Step 3: Clean up remaining related data using admin client
    // This is done after user deletion to ensure we can still authenticate above
    try {
      console.log(`Starting cleanup of related data for user: ${userId}`);

      // Delete in order to respect foreign key constraints
      // Note: Sessions are already cleaned up by Better Auth's deleteUser

      // 1. Delete OAuth accounts
      const accountsResult = await supabaseAdmin
        .from('accounts')
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${accountsResult.count || 0} OAuth accounts`);

      // 2. Delete subscriptions
      const subscriptionsResult = await supabaseAdmin
        .from('subscriptions')
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${subscriptionsResult.count || 0} subscriptions`);

      // 3. Delete one-time purchases
      const purchasesResult = await supabaseAdmin
        .from('one_time_purchases')
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${purchasesResult.count || 0} one-time purchases`);

      // 4. Delete any remaining sessions (cleanup)
      const sessionsResult = await supabaseAdmin
        .from('sessions')
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${sessionsResult.count || 0} remaining sessions`);

      // 5. Delete verifications (by email since they don't have user_id)
      const verificationsResult = await supabaseAdmin
        .from('verifications')
        .delete()
        .eq('identifier', userEmail);
      console.log(`Deleted ${verificationsResult.count || 0} verifications`);

      // 6. Delete GDPR-related data (GDPR compliance requirement)
      // User consents
      const consentsResult = await supabaseAdmin
        .from('user_consents' as any)
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${consentsResult.count || 0} user consents`);

      // Audit logs (keep deletion audit trail, but remove other user data)
      const auditLogsResult = await supabaseAdmin
        .from('audit_logs' as any)
        .delete()
        .eq('user_id', userId)
        .neq('action', 'account_deletion_started'); // Keep the deletion start log
      console.log(`Deleted ${auditLogsResult.count || 0} audit logs (keeping deletion trail)`);

      // Deletion requests
      const deletionRequestsResult = await supabaseAdmin
        .from('deletion_requests' as any)
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${deletionRequestsResult.count || 0} deletion requests`);

      // Cookie consents (only those with user_id, keep anonymous ones)
      const cookieConsentsResult = await supabaseAdmin
        .from('cookie_consents' as any)
        .delete()
        .eq('user_id', userId);
      console.log(`Deleted ${cookieConsentsResult.count || 0} cookie consents`);

      // CRITICAL: Ensure user is deleted from users table
      // This is the most important step for GDPR compliance
      const userDeletionResult = await supabaseAdmin
        .from('users')
        .delete()
        .eq('id', userId);

      if (userDeletionResult.error) {
        console.error('Failed to delete user from users table:', userDeletionResult.error);
        throw new Error(`Failed to delete user from users table: ${userDeletionResult.error.message}`);
      }

      console.log(`Successfully deleted user from users table: ${userId}`);
      console.log(`Successfully cleaned up all remaining data for user: ${userId}`);

    } catch (error) {
      console.error('Error cleaning up related data:', error);
      // This is critical - if user deletion from users table fails, we need to return an error
      if (error instanceof Error && error.message.includes('Failed to delete user from users table')) {
        return NextResponse.json(
          { error: "Failed to completely delete user account. Please contact support." },
          { status: 500 }
        );
      }
      // For other cleanup errors, log but don't fail the request
      console.log('User account was deleted successfully, but some cleanup failed');
    }

    // Step 4: Log successful deletion for audit purposes
    console.log('Account deletion completed:', {
      userId,
      email: userEmail,
      deletedAt: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });

    return NextResponse.json({
      success: true,
      message: "Account permanently deleted"
    });

  } catch (error: any) {
    console.error('Account deletion error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/account/delete
 * 
 * Get information about what data will be deleted
 * This helps users understand the scope of deletion
 */
export async function GET() {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get counts of data that will be deleted
    const subscriptions = await subscriptionOperations.findByUserId(userId);

    const deletionInfo = {
      user: {
        email: session.user.email,
        name: session.user.name,
        createdAt: session.user.createdAt
      },
      dataToDelete: {
        subscriptions: subscriptions.length,
        activeSubscriptions: subscriptions.filter(s => s.status === 'active').length,
        sessions: "All active sessions",
        oauthAccounts: "All connected accounts"
      },
      warning: "This action cannot be undone. All your data will be permanently deleted."
    };

    return NextResponse.json(deletionInfo);

  } catch (error: any) {
    console.error('Error getting deletion info:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
