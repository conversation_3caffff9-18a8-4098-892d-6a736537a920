/**
 * Test API for Account Deletion Verification
 * 
 * This endpoint helps test and verify that account deletion is working correctly.
 * Only available in development mode for security.
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyAccountDeletion } from '@/scripts/verify-account-deletion';

export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');

    switch (action) {
      case 'verify':
        const verification = await verifyAccountDeletion(userId || undefined);
        return NextResponse.json({
          success: true,
          verification
        });

      case 'list-users':
        const { data: users, error } = await supabaseAdmin
          .from('users')
          .select('id, email, name, created_at')
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        return NextResponse.json({
          success: true,
          users: users || []
        });

      case 'check-user-data':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId parameter is required for check-user-data' },
            { status: 400 }
          );
        }

        // Check all tables for user data
        const tables = [
          'users',
          'sessions', 
          'accounts',
          'subscriptions',
          'one_time_purchases',
          'verifications',
          'user_consents',
          'audit_logs',
          'deletion_requests',
          'cookie_consents'
        ];

        const userData: Record<string, any> = {};

        for (const table of tables) {
          try {
            let query;
            
            if (table === 'users') {
              query = supabaseAdmin
                .from(table)
                .select('*')
                .eq('id', userId);
            } else if (table === 'verifications') {
              // Get user email first
              const { data: user } = await supabaseAdmin
                .from('users')
                .select('email')
                .eq('id', userId)
                .single();
              
              if (user?.email) {
                query = supabaseAdmin
                  .from(table)
                  .select('*')
                  .eq('identifier', user.email);
              }
            } else {
              query = supabaseAdmin
                .from(table)
                .select('*')
                .eq('user_id', userId);
            }

            if (query) {
              const { data, error } = await query;
              
              if (error) {
                userData[table] = { error: error.message };
              } else {
                userData[table] = {
                  count: data?.length || 0,
                  records: data || []
                };
              }
            } else {
              userData[table] = { count: 0, records: [] };
            }
          } catch (error) {
            userData[table] = { error: String(error) };
          }
        }

        return NextResponse.json({
          success: true,
          userId,
          userData
        });

      default:
        return NextResponse.json({
          success: true,
          message: 'Account deletion test API',
          availableActions: [
            'verify - Verify account deletion compliance',
            'list-users - List all users in the system',
            'check-user-data?userId=xxx - Check all data for a specific user'
          ]
        });
    }

  } catch (error: any) {
    console.error('Account deletion test API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const body = await request.json();
    const { action, userId } = body;

    switch (action) {
      case 'create-test-user':
        // Create a test user for deletion testing
        const testUser = {
          id: `test-user-${Date.now()}`,
          email: `test-${Date.now()}@example.com`,
          name: 'Test User',
          email_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data: createdUser, error: createError } = await supabaseAdmin
          .from('users')
          .insert(testUser)
          .select()
          .single();

        if (createError) {
          throw createError;
        }

        // Create some test data for the user
        await supabaseAdmin
          .from('sessions')
          .insert({
            id: `session-${Date.now()}`,
            user_id: createdUser.id,
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            token: `token-${Date.now()}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        return NextResponse.json({
          success: true,
          testUser: createdUser,
          message: 'Test user created with sample data'
        });

      case 'cleanup-orphaned':
        // Clean up orphaned records
        const verification = await verifyAccountDeletion();
        let cleanedUp = 0;

        for (const orphaned of verification.orphanedRecords) {
          try {
            const { error } = await supabaseAdmin
              .from(orphaned.table)
              .delete()
              .in('user_id', orphaned.userIds);

            if (!error) {
              cleanedUp += orphaned.count;
            }
          } catch (error) {
            console.error(`Failed to cleanup ${orphaned.table}:`, error);
          }
        }

        return NextResponse.json({
          success: true,
          cleanedUp,
          message: `Cleaned up ${cleanedUp} orphaned records`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Account deletion test API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
