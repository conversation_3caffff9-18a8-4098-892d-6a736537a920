# Creem Webhook Flow During Account Deletion

## How Webhooks Work in Our Implementation

### **We DON'T send webhooks - We RECEIVE them from Creem**

```
┌─────────────────┐    API Call     ┌─────────────────┐
│   Our App       │ ──────────────► │     Creem       │
│ Account Delete  │                 │   (Payment)     │
└─────────────────┘                 └─────────────────┘
         ▲                                   │
         │                                   │ Webhook
         │ Webhook Response                  │
         │                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│ Our Webhook     │ ◄────────────── │ Creem Webhook   │
│ Handler         │                 │ Service         │
└─────────────────┘                 └─────────────────┘
```

## Complete Account Deletion Flow

### **Step-by-Step Process:**

```
1. User clicks "Delete Account"
   ↓
2. Our app calls Creem API: creem.cancelSubscription()
   ↓
3. Creem cancels the subscription in their system
   ↓
4. Our app updates local database: status = "canceled"
   ↓
5. Our app waits 2 seconds for webhooks
   ↓
6. Creem sends webhook: "subscription.canceled" 
   ↓
7. Our webhook handler receives and processes it
   ↓
8. Our app verifies all subscriptions are canceled
   ↓
9. Our app deletes user from all database tables
   ↓
10. Account deletion complete
```

## Webhook Events We Handle

### **subscription.canceled**
- **When**: Creem confirms subscription cancellation
- **What we do**: Update local database status to "canceled"
- **Timing**: Usually arrives within 1-2 seconds of API call

### **subscription.expired**
- **When**: Subscription naturally expires or fails payment
- **What we do**: Update local database status to "expired"
- **Timing**: At end of billing period or payment failure

## Race Condition Prevention

### **The Problem:**
```
Time 0: API call to cancel subscription
Time 1: Update local database
Time 2: Delete user from database
Time 3: Webhook arrives ← FAILS because user is gone!
```

### **Our Solution:**
```
Time 0: API call to cancel subscription
Time 1: Update local database immediately
Time 2: Wait 2 seconds for webhooks
Time 3: Webhook arrives and processes successfully
Time 4: Verify cancellation
Time 5: Delete user from database
```

## Error Handling

### **If Webhook Fails:**
- We already updated the local database
- Subscription is still canceled in Creem
- Account deletion proceeds safely
- No continued billing occurs

### **If API Call Fails:**
- Account deletion is ABORTED
- User data remains intact
- Subscription remains active (user can try again)
- No data loss, no billing issues

## Testing the Webhook Flow

### **1. Test Webhook Endpoint:**
```bash
# Check if webhook handler is working
curl -X POST http://localhost:3000/api/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-webhook",
    "eventType": "subscription.canceled",
    "object": {
      "id": "test-sub-123",
      "metadata": {"userId": "test-user"}
    }
  }'
```

### **2. Monitor Webhook Processing:**
- Check server logs during account deletion
- Look for "Webhook: Successfully updated subscription" messages
- Verify timing of webhook arrival vs account deletion

### **3. Test Race Conditions:**
- Create test subscription
- Delete account immediately
- Verify webhook is processed correctly
- Confirm no errors in logs

## Webhook Security

### **Webhook Validation:**
- Webhooks come from Creem's servers
- Should validate webhook signatures (if Creem provides them)
- Process only expected event types

### **Error Recovery:**
- Webhook failures don't stop account deletion
- Local database is updated before webhook arrives
- Graceful handling of missing subscriptions

## Production Considerations

### **Webhook Reliability:**
- Creem may retry failed webhooks
- Our handler should be idempotent
- Log all webhook events for debugging

### **Monitoring:**
- Monitor webhook processing times
- Alert on webhook failures
- Track subscription cancellation success rates

### **Backup Plans:**
- If webhooks fail consistently, we still have API confirmation
- Manual verification tools available
- Database consistency maintained

---

## Summary

**We don't send webhooks during account deletion. Instead:**

1. **We call Creem API** to cancel subscriptions
2. **Creem sends us webhooks** to confirm cancellation
3. **We process the webhooks** to update our database
4. **We wait for webhooks** before deleting the user
5. **Account deletion proceeds** only after confirmation

This ensures both systems stay in sync and prevents billing issues while maintaining data integrity.
